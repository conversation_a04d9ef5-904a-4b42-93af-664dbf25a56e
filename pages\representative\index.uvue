<template>
	<!-- <view class=""> -->
		
		<view class="status_bar">
			
		</view>
		<view class="nav-bar">
		    
		    <view class="nav-btn" @tap="handleBack">
		      <text class="iconfont icon_font">&#xe696;</text>
		    </view>
			<view class="title_content" @click="handelSelect">
				<text class="title">{{title}}</text>
				<text class="iconfont xiala_icon">&#xe600;</text>
			</view>
		
		    <view class="nav-btn" >
				<!-- <text class="iconfont icon_font" v-if="isEditOrDetails=='2'">&#xe7e7;</text> -->
		    </view>
		</view>
		

		<scrollRefresh v-if="title_id!='3'" :isRefreshing="refreshing"
		  :isLoading="loading"
		  :hasMore="hasMore"
		  @refresh="onRefreshTriggered"
		  @loadMore="onLoadMoreTriggered">
			<!-- 自定义列表内容 -->
			 <view class="content-list" v-if="title_id=='1'">
				  <view class="rb_item" v-for="(item,index) in listData" :key="index">
				  	<view class="rb_item_content">
				  		<text class="rb_item_label">清单名称：</text>
						<text class="rb_item_title">{{item.name}}</text>
				  	</view>
					<view class="rb_item_content">
						<text class="rb_item_label">提交时间：</text>
						<text class="rb_item_title">{{item.time}}</text>
					</view>
					<view class="rb_item_content">
						<text class="rb_item_label">提交人：</text>
						<text class="rb_item_title">{{item.nickName}}</text>
					</view>
					<view class="rb_item_content">
						<text class="rb_item_label">系统打分：</text>
						<text class="rb_item_title lv">{{item.score}}</text>
					</view>
				  </view>
			  </view>
			   <view class="content-list" v-if="title_id=='2'">
				     <view class="zb_item" @click="handledetails(2,item)" v-for="(item,index) in listData" :key="index">
						 <view class="zb_item_label">
						 	<text class="zb_name">{{item.nickName}}</text>
							<text class="zb_gw">
								{{item.gw}}
							</text>

						 </view>
						 <text class="zb_item_title">
						 	{{item.title}}
						 </text>
					 </view>
				</view>
				<!-- 任务中心内容 -->
				<view v-if="title_id=='4'">
					<!-- 状态筛选tabs -->
					<view class="status-tabs-container">
						<scroll-view
							class="status-tabs-scroll"
							direction="horizontal"
							:show-scrollbar="false"
						>
							<view class="status-tabs">
								<view
									class="status-tab-item"
									v-for="(item,index) in statusOptions"
									:key="index"
									@click="handleStatusChange(item.value)"
								>
									<text class="status-tab-text" :class="currentStatus == item.value ? 'status-tab-text-active' : ''">{{item.label}}</text>
									<view class="status-tab-underline" v-if="currentStatus == item.value"></view>
								</view>
							</view>
						</scroll-view>
					</view>

					<!-- 任务列表 -->
					<view class="content-list">
						<view class="task_item" v-for="(item,index) in listData" :key="index">
							<view class="task_item_content">
								<text class="task_item_label">任务名称：</text>
								<text class="task_item_title">{{item.taskName}}</text>
							</view>
							<view class="task_item_content">
								<text class="task_item_label">清单名称：</text>
								<text class="task_item_title">{{item.inventoryName}}</text>
							</view>
							<view class="task_item_content">
								<text class="task_item_label">开始时间：</text>
								<text class="task_item_title">{{item.startTime}}</text>
							</view>
							<view class="task_item_content">
								<text class="task_item_label">结束时间：</text>
								<text class="task_item_title">{{item.endTime}}</text>
							</view>
						</view>
					</view>
				</view>
		</scrollRefresh>
		
		<scroll-view style="flex:1" v-if="title_id=='3'">
			
			<view v-if="title_id=='3'" class="personneplApproval">
				<view class="toExamine_content" v-if="handleEmploymentApproval()">
					<view class="toExamine_header">
						<text class="iconfont toExamine_icon">&#xe640;</text>
						<text class="toExamine_label">入职审批</text>
					</view>
					<view class="toExamine_list">
						<view class="toExamine_list_item" @click="handleapprove(1,item)" v-for="(item,index) in personneplApproval.employmentApproval" :key="index">
							<view class="toExamine_list_item_header">
								<text class="list_item_name">{{item.nickName}}</text>
								<text class="list_item_gw">{{item.post}}</text>
							</view>
							<view class="toExamine_list_item_content">
								<view class="form_view">
									<text class="form_label">所属项目部：</text>
									<text class="form_content">{{item.dept}}</text>
								</view>
								<view class="form_view">
									<text class="form_label">提交时间：</text>
									<text class="form_content">{{item.time}}</text>
								</view>
							</view>
						</view>
						
					</view>
				</view>
				<view class="toExamine_content" style="margin-top: 20rpx;" v-if="handlestaffAdjust()">
					<view class="toExamine_header">
						<text class="iconfont toExamine_icon">&#xe61d;</text>
						<text class="toExamine_label">员工调整</text>
					</view>
					<view class="toExamine_list">
						<view class="toExamine_list_item" @click="handleapprove(2,item)" v-for="(item,index) in personneplApproval.staffAdjust" :key="index">
							<view class="toExamine_list_item_header">
								<text class="list_item_name">{{item.nickName}}</text>
								<text class="list_item_gw">{{item.post}}</text>
							</view>
							<view class="toExamine_list_item_content">
								<view class="form_view">
									<text class="form_label">所属项目部：</text>
									<text class="form_content">{{item.dept}}</text>
								</view>
								<view class="form_view">
									<text class="form_label">提交时间：</text>
									<text class="form_content">{{item.time}}</text>
								</view>
							</view>
						</view>
						
					</view>
				</view>
			
			</view>
		</scroll-view>
		<selectPicker v-if="showPicker"  :show="showPicker" @close="pickerColse" @confirm="pickerVonfirm" :items="titleList" :value="title_id"></selectPicker>
	
	<!-- </view> -->
</template>

<script setup>
	import {ProjectOption,confirmHcType,pageType,personneplApprovalType,employmentApprovalType,staffAdjustType} from '@/utils/apiType.uts'
	import selectPicker from '@/components/select_picker_gj.uvue'
	import scrollRefresh from '@/components/scroll-refresh.uvue'
	
	
	// 定义数据结构
	type ListItem = {
	  id: number
	  name?: string,
	  time?: string,
	  nickName?:string,
	  score?: string|number,
	  gw?:string,
	  title?:string,
	  // 任务中心相关字段
	  taskUserId?: string,
	  taskName?: string,
	  taskType?: string,
	  inventoryName?: string,
	  startTime?: string,
	  endTime?: string,
	}
	const title=ref<string>('任务中心')
	// 选中的id
	const title_id=ref<string|number|null>('4')
	
	// 选择的数据
	const titleList =ref<ProjectOption[]>([
		{value:'1',label:'日报评价'},
		{value:'2',label:'周报评价'},
		{value:'3',label:'人事审批'},
		{value:'4',label:'任务中心'},
	])
	
	const showPicker=ref<boolean>(false)
	const selectActive=ref<ProjectOption|null>(null)
	
	
	const refreshing = ref(false)
	const loading = ref(false)
	const hasMore = ref(true)
	const listData = ref<ListItem[]>([]) // 您的数据
	// 分页
	const pageInfo=ref<pageType>({
		page:1,
		pageSize:15,
		total:20
	})

	// 任务中心状态筛选
	type StatusOption = {
		value: string,
		label: string
	}

	const statusOptions = ref<StatusOption[]>([
		{value: '', label: '全部'},
		{value: '0', label: '未开始'},
		{value: '1', label: '进行中'},
		{value: '2', label: '已完成'},
		{value: '3', label: '转办中'},
		{value: '4', label: '以转办'},
		{value: '5', label: '已取消'},
		{value: '6', label: '已超期'}
	])

	const currentStatus = ref<string>('')
	
	// 人事审批数据
	const personneplApproval=ref<personneplApprovalType>({
		employmentApproval:[
			{id:1,nickName:'张某某',post:'土建工程师',dept:'一二三项目部',time:'2020.01.02 14:20:30'},
			{id:2,nickName:'李某某',post:'水电工程师',dept:'一二三项目部',time:'2020.01.02 14:20:30'},
		],
		staffAdjust:[
			{id:1,nickName:'张某某',post:'施工员',dept:'一二三项目部',time:'2020.01.02 14:20:30'},
		]
	})
	// 判断员工调整 是否存在数据
	const handlestaffAdjust=()=>{
		if(personneplApproval.value.staffAdjust!=null){
			if(personneplApproval.value.staffAdjust.length>0){
				return true
			}else{
				return false
			}
		}else{
			return true
		}
	}
	
	// 判断入职审批，是否存在数据
	const handleEmploymentApproval=()=>{
		if(personneplApproval.value.employmentApproval!=null){
			if(personneplApproval.value.employmentApproval.length>0){
				return true
			}else{
				return false
			}
		}else{
			return true
		}
	}
	
	// 入职审批
	const handleapprove=(i:number,item:employmentApprovalType|staffAdjustType)=>{
		if(i==1){
			uni.navigateTo({
				url:'/pages/representative/approve/rzApprove?id='+item['id']
			})
		}else if(i==2){
			uni.navigateTo({
				url:'/pages/representative/approve/ygtzApprove?id='+item['id']
			})
		}
	}
	
	// 获取数据函数
	const fetchData = () => {

		if (loading.value || !hasMore.value) return
		loading.value=true
		setTimeout(() => {
			const newData: ListItem[] = []
			for (let i = 0; i < pageInfo.value.pageSize; i++) {
				 const id = (pageInfo.value.page - 1) * pageInfo.value.pageSize+ i + 1

				 // 根据不同的title_id生成不同的数据
				 if (title_id.value == '4') {
					 // 任务中心数据 - 根据状态生成不同数据
					 let taskName = '土方工程填土任务'

					 // 根据当前状态生成对应的任务名称
					 if (currentStatus.value == '0') {
						 taskName = '未开始的土方工程任务'
					 } else if (currentStatus.value == '1') {
						 taskName = '进行中的土方工程任务'
					 } else if (currentStatus.value == '2') {
						 taskName = '已完成的土方工程任务'
					 } else if (currentStatus.value == '3') {
						 taskName = '转办中的土方工程任务'
					 } else if (currentStatus.value == '4') {
						 taskName = '以转办的土方工程任务'
					 } else if (currentStatus.value == '5') {
						 taskName = '已取消的土方工程任务'
					 } else if (currentStatus.value == '6') {
						 taskName = '已超期的土方工程任务'
					 } else {
						 taskName = '土方工程填土任务'
					 }

					 newData.push({
					   id: id,
					   taskUserId: '1954836128834035713',
					   taskName: taskName,
					   taskType: '1',
					   inventoryName: '2025年2月13日清单',
					   startTime: '2025年2月13日',
					   endTime: '2025年2月13日'
					 })
				 } else {
					 // 其他页面的原有数据
					 newData.push({
					   id: id,
					   name:'2025年2月13日清单',
					   time:'2025年2月13日 18:00',
					   nickName:'张某某',
					   score:152,
					   gw:'土建工程师',
					   title:'2025年1月份第一周考核'
					 })
				 }
			}
			listData.value = pageInfo.value.page === 1
				 ? newData
				 : [...listData.value, ...newData]

			// 数据结束条件（可调整或移除）
			if(listData.value.length>=pageInfo.value.total){
				hasMore.value = false
			}
			// if (pageInfo.value.page >= 5) hasMore.value = false

			loading.value = false
			refreshing.value = false
			pageInfo.value.page++

		}, 800)

	}
	
	// 下拉刷新事件
	const onRefreshTriggered = () => {
	  if (refreshing.value) return
	  
	  refreshing.value = true
	  pageInfo.value.page = 1
	  hasMore.value = true
	  pageInfo.value.pageSize = 15 // 重置为初始值
	  
	  setTimeout(() => {
	    fetchData()
	  }, 1000)
	}
	
	// 上拉加载更多事件
	const onLoadMoreTriggered = () => {
		 if (!hasMore.value || loading.value) return
		 
		 // 每次加载固定数量 (无需修改pageSize)
		 pageInfo.value.pageSize = 15
		 fetchData()
	}
	
	// 打开弹窗组件
	const handelSelect=()=>{
		showPicker.value=true
	}
	
	// 关闭选择组件
	const pickerColse=()=>{
		showPicker.value=false
	}
	// 回传关闭组件
	const pickerVonfirm=(data:confirmHcType)=>{
		console.log('data',data);
		
		if(data.selectedItem!=null){
			const label=data.selectedItem['label']
			if (label != null) {
			  title.value = label as string
			}
			const value=data.selectedItem['value']
			if (value != null) {
			  title_id.value = value as string
			}
		}
		pageInfo.value.page=1
		listData.value=[]
		onRefreshTriggered()
		showPicker.value=false
	
	}
	
	const handleBack=()=> {
	  uni.navigateBack()
	}
	
	
	const handledetails=(i:number,item:ListItem)=>{
		if(i==1){

		}else if(i==2){
			uni.navigateTo({
				url:'/pages/representative/examine/weekExamineDetails?id='+item.id
			})
		}
	}

	// 状态切换处理函数
	const handleStatusChange = (status: string) => {
		currentStatus.value = status
		pageInfo.value.page = 1
		listData.value = []
		hasMore.value = true
		onRefreshTriggered()
	}
	
	// 页面加载时初始化数据
	onLoad((options: OnLoadOptions) => {
		
		fetchData()
	})
</script>

<style lang="scss" scoped>
/* 关键修复：添加容器层 */
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.nav-bar {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  // height: 88px;
  padding: 30rpx 32rpx;
  background-color: #FFFFFF;
  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE;
  /* 修复1：移除fixed定位 */
  // position: relative;
  // width: 100%;
  // z-index: 99;
}

/* 修复2：滚动区域高度设置 */
.content-scroll {
  flex: 1; 
  width: 100%;
  // height: 1px;
  /* 确保内容不被导航栏遮挡 */
  box-sizing: border-box;
}

/* 修复3：添加内容项样式 */
.content-item {
  height: 200px;
  background: red;
  margin-bottom: 10px;
}

/* 其他原有样式保持不变 */
.title_content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 50%;
}

.title {
  font-size: 36rpx;
  color: #131313;
}

.icon_font {
  font-size: 32rpx;
  color: #000000;
}

.xiala_icon {
  font-size: 32rpx;
  color: #CBCBCB;
}

/* 内容列表 */
.content-list {
  padding: 20rpx;
  background: #f3f5f8;
}
.rb_item{
	background: #FFFFFF;
	padding: 30rpx 30rpx;
	box-shadow: 5rpx 11rpx 17rpx 1rpx rgba(208,223,241,0.23);
	margin-bottom: 20rpx;
	.rb_item_content{
		display: flex;
		flex-direction: row;
		
		margin-bottom: 20rpx;
		
	}
	.rb_item_label{
		color: #8B8B8B;
		font-size: 28rpx;
	}
	.rb_item_title{
		color: #131313;
		font-size: 28rpx;
	}
}

.lv{
	color: #00AF23 !important;
	font-weight: 700;
}

/* 任务中心状态筛选tabs样式 */
.status-tabs-container {
	background: #FFFFFF;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #EEEEEE;
}

.status-tabs-scroll {
	width: 100%;
	height: 80rpx;
	flex-direction: row;
}

.status-tabs {
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: 0 20rpx;
}

.status-tab-item {
	flex-shrink: 0;
	padding: 15rpx 30rpx;
	margin-right: 40rpx;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.status-tab-text {
	font-size: 28rpx;
	color: #666666;
	white-space: nowrap;
}

.status-tab-text-active {
	color: #309ff3;
	font-weight: 700;
}

.status-tab-underline {
	width: 60%;
	height: 4rpx;
	background: #309ff3;
	margin-top: 8rpx;
}

/* 任务中心样式 */
.task_item{
	background: #FFFFFF;
	padding: 30rpx 30rpx;
	box-shadow: 5rpx 11rpx 17rpx 1rpx rgba(208,223,241,0.23);
	margin-bottom: 20rpx;
	.task_item_content{
		display: flex;
		flex-direction: row;
		margin-bottom: 20rpx;
	}
	.task_item_label{
		color: #8B8B8B;
		font-size: 28rpx;
	}
	.task_item_title{
		color: #131313;
		font-size: 28rpx;
	}
}
.zb_item{
	background: #FFFFFF;
	padding: 30rpx 30rpx;
	box-shadow: 5rpx 11rpx 17rpx 1rpx rgba(208,223,241,0.23);
	margin-bottom: 20rpx;
	.zb_item_label{
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		align-items: center;
		overflow: visible;
		
	}
	.zb_name{
		color: #131313;
		font-size: 28rpx;
		font-weight: 700;
	}
	.zb_gw{
		font-size: 27rpx;
		padding: 5rpx 10rpx;
		color: #FFB300;
		background: #FFF6D6;
		margin-left: 5rpx;
	}
	.zb_item_title{
		color:#131313;
		font-size: 27rpx;
		margin-top: 20rpx;
	}
}
.list-item {
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.item-index {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 10rpx;
}

.item-title {
  font-size: 34rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 15rpx;
}

.item-content {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 40rpx;
}
.personneplApproval{
	width: 100%;
	padding: 20rpx 40rpx;
	background: #F7F8FA;
	.toExamine_content{
		width:100%;
		.toExamine_header{
			display: flex;
			flex-direction: row;
			align-items: center;
			.toExamine_icon{
				font-size: 34rpx;
				color:#309ff3;
			}
			.toExamine_label{
				color: #131313;
				font-size: 30rpx;
				margin-left: 10rpx;
			}
		}
		.toExamine_list{
			width: 100%;
			margin-top: 20rpx;
			.toExamine_list_item{
				width: 96%;
				margin: 0 auto;
				padding: 20rpx 40rpx;
				background: #FFFFFF;
				box-shadow: 5rpx 11rpx 17rpx 1rpx rgba(208,223,241,0.23);
				margin-bottom: 30rpx;
				.toExamine_list_item_header{
					display: flex;
					flex-direction: row;
					align-items: center;
					padding: 20rpx 0;
					border-bottom: 1rpx solid #E3E7EC;
					.list_item_name{
						color:#131313;
						font-size: 30rpx;
						font-weight: bold;
					}
					.list_item_gw{
						margin-left: 10rpx;
						padding: 10rpx 20rpx;
						background: #FFF6D6;
						font-size: 30rpx;
						color: #FFB300;
						border-radius: 10rpx;
					}
				}
				.toExamine_list_item_content{
					margin-top: 50rpx;
					.form_view{
						width: 100%;
						display: flex;
						flex-direction: row;
						margin-bottom: 20rpx;
						
						.form_label{
							font-size: 28rpx;
							color: #8B8B8B;
						}
						.form_content{
							flex: 1;
							font-size: 28rpx;
							color: #131313;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							
						}
					}
				}
			}
		}
	}
	
}

</style>
