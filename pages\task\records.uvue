<template>
	<view class="page-container">
		<view class="status_bar"></view>
		
		<!-- 自定义导航栏 -->
		<view class="nav-bar">
			<view class="nav-left" @click="handleBack">
				<text class="iconfont icon_font">&#xe696;</text>
			</view>
			<view class="nav-center">
				<text class="nav-title">下发记录</text>
			</view>
			<view class="nav-right">
				<!-- 占位 -->
			</view>
		</view>

		<!-- 使用scroll-refresh组件 -->
		<scrollRefresh
			:isRefreshing="refreshing"
			:isLoading="loading"
			:hasMore="hasMore"
			@refresh="onRefreshTriggered"
			@loadMore="onLoadMoreTriggered">

			<!-- 列表内容 -->
			<view class="records-list" v-if="recordsList.length > 0">
				<view class="record-item" v-for="(item, index) in recordsList" :key="item.taskId">
					<view class="info-row">
						<text class="info-label">任务名称：</text>
						<text class="info-value">{{ item.taskName }}</text>
					</view>
					<view class="info-row">
						<text class="info-label">下发时间：</text>
						<text class="info-value">{{ formatDateTime(item.createTime) }}</text>
					</view>
					<view class="info-row">
						<text class="info-label">任务周期：</text>
						<text class="info-value">{{ formatDateRange(item.startTime, item.endTime) }}</text>
					</view>
				</view>
			</view>

			<!-- 空数据状态 -->
			<view class="empty-container" v-else-if="!loading && !refreshing">
				<text class="empty-text">暂无下发记录</text>
			</view>
		</scrollRefresh>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { TaskRecordItem, pageType } from '@/utils/apiType.uts'
import scrollRefresh from '@/components/scroll-refresh.uvue'

// 页面状态
const refreshing = ref(false)
const loading = ref(false)
const hasMore = ref(true)

// 数据列表
const recordsList = ref<TaskRecordItem[]>([])

// 分页信息
const pageInfo = ref<pageType>({
	page: 1,
	pageSize: 15,
	total: 0
})

// 模拟数据
const mockData: TaskRecordItem[] = [
	{
		taskId: "1954836807317233665",
		taskName: "土方工程填土任务",
		taskType: "1",
		startTime: "2025-02-03 00:00:00",
		endTime: "2025-03-03 00:00:00",
		createTime: "2025-02-03 02:20:30"
	},
	{
		taskId: "1954836127886123009",
		taskName: "土方工程填土任务",
		taskType: "1",
		startTime: "2025-02-03 00:00:00",
		endTime: "2025-03-03 00:00:00",
		createTime: "2025-02-03 02:20:30"
	},
	{
		taskId: "1954836127886123010",
		taskName: "桥梁基础施工",
		taskType: "2",
		startTime: "2025-02-04 00:00:00",
		endTime: "2025-03-04 00:00:00",
		createTime: "2025-02-04 08:15:20"
	},
	{
		taskId: "1954836127886123011",
		taskName: "隧道开挖作业",
		taskType: "1",
		startTime: "2025-02-05 00:00:00",
		endTime: "2025-03-05 00:00:00",
		createTime: "2025-02-05 14:30:45"
	},
	{
		taskId: "1954836127886123012",
		taskName: "路面铺设工程",
		taskType: "3",
		startTime: "2025-02-06 00:00:00",
		endTime: "2025-03-06 00:00:00",
		createTime: "2025-02-06 09:45:12"
	}
]

// 生成更多模拟数据
const generateMoreMockData = (startIndex: number, count: number): TaskRecordItem[] => {
	const moreData: TaskRecordItem[] = []
	const taskNames = ["土方工程填土任务", "路基50米处", "桥梁基础施工", "隧道开挖作业", "路面铺设工程", "钢筋绑扎作业", "混凝土浇筑", "模板安装工程"]
	
	for (let i = 0; i < count; i++) {
		const index = startIndex + i
		const nameIndex = index % taskNames.length
		const dayOffset = Math.floor(index / taskNames.length)
		
		moreData.push({
			taskId: `195483612788612${3000 + index}`,
			taskName: taskNames[nameIndex],
			taskType: ((index % 3) + 1).toString(),
			startTime: `2025-02-${(7 + dayOffset).toString().padStart(2, '0')} 00:00:00`,
			endTime: `2025-03-${(7 + dayOffset).toString().padStart(2, '0')} 00:00:00`,
			createTime: `2025-02-${(7 + dayOffset).toString().padStart(2, '0')} ${(8 + (index % 12)).toString().padStart(2, '0')}:${(15 + (index % 45)).toString().padStart(2, '0')}:${(10 + (index % 50)).toString().padStart(2, '0')}`
		})
	}
	
	return moreData
}

// 格式化日期范围
const formatDateRange = (startTime: string, endTime: string): string => {
	const start = startTime.split(' ')[0]
	const end = endTime.split(' ')[0]
	return `${start}-${end}`
}

// 格式化日期时间
const formatDateTime = (dateTime: string): string => {
	// 将 "2025-02-03 02:20:30" 格式化为 "2025/2/3 02:20:30"
	const parts = dateTime.split(' ')
	if (parts.length == 2) {
		const datePart = parts[0].split('-')
		if (datePart.length == 3) {
			const year = datePart[0]
			const month = parseInt(datePart[1]).toString() // 去掉前导0
			const day = parseInt(datePart[2]).toString() // 去掉前导0
			return `${year}/${month}/${day} ${parts[1]}`
		}
	}
	return dateTime
}

// 加载数据
const loadData = (isRefresh: boolean): void => {
	if (isRefresh) {
		pageInfo.value.page = 1
		recordsList.value = []
		hasMore.value = true
	}
	
	// 模拟API调用延迟
	setTimeout(() => {
		const startIndex = (pageInfo.value.page - 1) * pageInfo.value.pageSize
		let newData: TaskRecordItem[] = []
		
		if (pageInfo.value.page == 1) {
			// 第一页使用预定义的模拟数据
			newData = mockData.slice(0, pageInfo.value.pageSize)
		} else {
			// 后续页面生成新的模拟数据
			const remainingFromMock = Math.max(0, mockData.length - startIndex)
			if (remainingFromMock > 0) {
				newData = mockData.slice(startIndex, startIndex + pageInfo.value.pageSize)
			}
			
			if (newData.length < pageInfo.value.pageSize) {
				const additionalCount = pageInfo.value.pageSize - newData.length
				const additionalData = generateMoreMockData(startIndex + newData.length, additionalCount)
				newData = newData.concat(additionalData)
			}
		}
		
		if (isRefresh) {
			recordsList.value = newData
		} else {
			recordsList.value = recordsList.value.concat(newData)
		}
		
		// 模拟总数据量限制
		const totalMockDataCount = 50
		if (recordsList.value.length >= totalMockDataCount) {
			hasMore.value = false
		}
		
		pageInfo.value.page++
		refreshing.value = false
		loading.value = false
		
		console.log('数据加载完成，当前数据量:', recordsList.value.length)
	}, 1000)
}

// 下拉刷新
const onRefreshTriggered = (): void => {
	if (refreshing.value) return
	refreshing.value = true
	console.log('触发下拉刷新')
	loadData(true)
}

// 上拉加载更多
const onLoadMoreTriggered = (): void => {
	if (loading.value || !hasMore.value) return
	loading.value = true
	console.log('触发上拉加载更多')
	loadData(false)
}

// 返回上一页
const handleBack = (): void => {
	uni.navigateBack()
}

// 页面加载时初始化数据
onMounted(() => {
	console.log('下发记录页面加载')
	loadData(true)
})
</script>

<style scoped>
.page-container {
	width: 100%;
	height: 100%;
	background-color: #f5f5f5;
}

.status_bar {
	height: var(--status-bar-height);
	background-color: #ffffff;
}

/* 导航栏样式 */
.nav-bar {
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	height: 88rpx;
	padding: 0 30rpx;
	background-color: #ffffff;
	border-bottom-width: 1rpx;
	border-bottom-color: #eeeeee;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 88rpx;
	justify-content: center;
	align-items: center;
}

.nav-center {
	flex: 1;
	justify-content: center;
	align-items: center;
}

.nav-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
}

.icon_font {
	font-size: 40rpx;
	color: #333333;
}

/* 列表样式 */
.records-list {
	padding: 20rpx;
}

.record-item {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.info-row {
	flex-direction: row;
	align-items: center;
	margin-bottom: 16rpx;
}

.info-label {
	font-size: 28rpx;
	color: #8B8B8B;
	width: 160rpx;
}

.info-value {
	font-size: 28rpx;
	color: #131313;
	flex: 1;
}

/* 空数据状态 */
.empty-container {
	justify-content: center;
	align-items: center;
	height: 400rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999999;
}
</style>
